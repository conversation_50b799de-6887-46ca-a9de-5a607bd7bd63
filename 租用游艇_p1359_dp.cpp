#include <iostream>
#include <algorithm>
using namespace std;

int main() {
    int n;
    cin >> n;
    
    // 存储租金矩阵，r[i][j]表示从站点i到站点j的租金
    int r[205][205];
    
    // 读取租金矩阵（上三角矩阵）
    for(int i = 1; i < n; i++) {
        for(int j = i + 1; j <= n; j++) {
            cin >> r[i][j];
        }
    }
    
    // dp[i]表示从站点1到站点i的最少租金
    int dp[205];
    
    // 初始化
    for(int i = 1; i <= n; i++) {
        dp[i] = 1000000; // 设为无穷大
    }
    dp[1] = 0; // 起点租金为0
    
    // 动态规划
    for(int i = 2; i <= n; i++) {
        for(int j = 1; j < i; j++) {
            dp[i] = min(dp[i], dp[j] + r[j][i]);
        }
    }
    
    cout << dp[n] << endl;
    return 0;
}