#include <iostream>
#include <algorithm>
using namespace std;

int main() {
    int n, m;
    cin >> n >> m;
    
    int v[30], p[30]; // 价格和重要度
    for(int i = 1; i <= m; i++) {
        cin >> v[i] >> p[i];
    }
    
    // dp[j] 表示在钱数不超过j的情况下能获得的最大价值
    int dp[30005] = {0};
    
    // 0-1背包：遍历每件物品
    for(int i = 1; i <= m; i++) {
        // 从后往前遍历钱数（避免重复使用同一物品）
        for(int j = n; j >= v[i]; j--) {
            dp[j] = max(dp[j], dp[j - v[i]] + v[i] * p[i]);
        }
    }
    
    cout << dp[n] << endl;
    return 0;
}