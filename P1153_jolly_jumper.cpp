#include <iostream>
#include <cmath>
using namespace std;

int main() {
    int n;
    while(cin >> n) {
        int arr[1005];
        bool used[1005] = {false}; // 标记差值是否出现过
        
        // 读取数组
        for(int i = 0; i < n; i++) {
            cin >> arr[i];
        }
        
        // 特殊情况：只有一个元素时，认为是Jolly
        if(n == 1) {
            cout << "Jolly" << endl;
            continue;
        }
        
        // 计算相邻元素差的绝对值并标记
        for(int i = 0; i < n - 1; i++) {
            int diff = abs(arr[i + 1] - arr[i]);
            
            // 只标记在有效范围内的差值
            if(diff >= 1 && diff <= n - 1) {
                used[diff] = true;
            }
        }
        
        // 检查[1, n-1]范围内的所有值是否都出现过
        bool isJolly = true;
        for(int i = 1; i <= n - 1; i++) {
            if(!used[i]) {
                isJolly = false;
                break;
            }
        }
        
        if(isJolly) {
            cout << "Jolly" << endl;
        } else {
            cout << "Not jolly" << endl;
        }
    }
    
    return 0;
}