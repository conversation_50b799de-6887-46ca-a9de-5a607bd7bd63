#include <iostream>
#include <algorithm>
using namespace std;

int main() {
    int n, m;
    cin >> n >> m;
    
    double scores[105];  // 存储每个选手的最终得分
    int player[105];     // 存储选手编号
    
    // 计算每个选手的得分
    for(int i = 1; i <= n; i++) {
        int judgeScores[105];
        
        // 读取第i个选手的所有评委分数
        for(int j = 0; j < m; j++) {
            cin >> judgeScores[j];
        }
        
        // 找出最大值和最小值
        int maxScore = judgeScores[0];
        int minScore = judgeScores[0];
        int totalSum = 0;
        
        for(int j = 0; j < m; j++) {
            totalSum += judgeScores[j];
            if(judgeScores[j] > maxScore) {
                maxScore = judgeScores[j];
            }
            if(judgeScores[j] < minScore) {
                minScore = judgeScores[j];
            }
        }
        
        // 去掉最大值和最小值后计算平均分
        scores[i] = (double)(totalSum - maxScore - minScore) / (m - 2);
        player[i] = i;  // 记录选手编号
    }
    
    // 排序：按得分从高到低，得分相同时按编号从小到大
    for(int i = 1; i <= n; i++) {
        for(int j = i + 1; j <= n; j++) {
            // 如果j选手得分更高，或者得分相同但编号更小
            if(scores[player[j]] > scores[player[i]] || 
               (scores[player[j]] == scores[player[i]] && player[j] < player[i])) {
                // 交换选手位置
                int temp = player[i];
                player[i] = player[j];
                player[j] = temp;
            }
        }
    }
    
    // 输出排名
    for(int i = 1; i <= n; i++) {
        cout << player[i];
        if(i < n) cout << " ";
    }
    cout << endl;
    
    return 0;
}