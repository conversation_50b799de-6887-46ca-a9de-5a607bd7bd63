#include <iostream>
#include <string>
using namespace std;

int main() {
    int n;
    cin >> n;
    
    string names[105];           // 学生姓名
    int finalScores[105];        // 期末成绩
    int classScores[105];        // 班级评议成绩
    char isCadre[105];           // 是否学生干部
    char isWest[105];            // 是否西部学生
    int papers[105];             // 论文数
    int scholarships[105];       // 每个学生的奖学金总额
    
    int maxScholarship = 0;      // 最高奖学金
    int maxIndex = 0;            // 获得最高奖学金的学生索引
    int totalScholarship = 0;    // 所有学生奖学金总和
    
    // 读取学生信息并计算奖学金
    for(int i = 0; i < n; i++) {
        cin >> names[i] >> finalScores[i] >> classScores[i] 
            >> isCadre[i] >> isWest[i] >> papers[i];
        
        int currentScholarship = 0;
        
        // 1. 院士奖学金：期末成绩>80 且 论文≥1
        if(finalScores[i] > 80 && papers[i] >= 1) {
            currentScholarship += 8000;
        }
        
        // 2. 五四奖学金：期末成绩>85 且 班级评议>80
        if(finalScores[i] > 85 && classScores[i] > 80) {
            currentScholarship += 4000;
        }
        
        // 3. 成绩优秀奖：期末成绩>90
        if(finalScores[i] > 90) {
            currentScholarship += 2000;
        }
        
        // 4. 西部奖学金：期末成绩>85 且 是西部学生
        if(finalScores[i] > 85 && isWest[i] == 'Y') {
            currentScholarship += 1000;
        }
        
        // 5. 班级贡献奖：班级评议>80 且 是学生干部
        if(classScores[i] > 80 && isCadre[i] == 'Y') {
            currentScholarship += 850;
        }
        
        scholarships[i] = currentScholarship;
        totalScholarship += currentScholarship;
        
        // 更新最高奖学金获得者（出现最早的）
        if(currentScholarship > maxScholarship) {
            maxScholarship = currentScholarship;
            maxIndex = i;
        }
    }
    
    // 输出结果
    cout << names[maxIndex] << endl;
    cout << maxScholarship << endl;
    cout << totalScholarship << endl;
    
    return 0;
}