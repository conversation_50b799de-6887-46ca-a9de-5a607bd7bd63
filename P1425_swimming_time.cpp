#include <iostream>
using namespace std;

int main() {
    int a, b, c, d;
    cin >> a >> b >> c >> d;
    
    // 将开始时间和结束时间都转换为分钟数
    int startMinutes = a * 60 + b;  // 开始时间的总分钟数
    int endMinutes = c * 60 + d;    // 结束时间的总分钟数
    
    // 计算游泳的总分钟数
    int totalMinutes = endMinutes - startMinutes;
    
    // 将总分钟数转换为小时和分钟
    int hours = totalMinutes / 60;      // 小时数
    int minutes = totalMinutes % 60;    // 剩余分钟数
    
    cout << hours << " " << minutes << endl;
    
    return 0;
}