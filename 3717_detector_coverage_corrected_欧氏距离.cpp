#include <iostream>
using namespace std;

int main() {
    int n, m, r;
    cin >> n >> m >> r;
    
    int detectorX[105], detectorY[105];  // 存储探测器坐标
    
    // 读取探测器坐标
    for(int i = 0; i < m; i++) {
        cin >> detectorX[i] >> detectorY[i];
    }
    
    int count = 0;  // 能被探测到的点的数量
    
    // 遍历网格上的每个点
    for(int x = 1; x <= n; x++) {
        for(int y = 1; y <= n; y++) {
            bool canDetect = false;  // 当前点是否能被探测到
            
            // 检查是否在任意一个探测器的范围内
            for(int i = 0; i < m; i++) {
                // 计算当前点到探测器的欧几里得距离的平方
                int dx = x - detectorX[i];
                int dy = y - detectorY[i];
                int distanceSquared = dx * dx + dy * dy;
                
                // 判断是否在探测范围内：距离² ≤ r²
                if(distanceSquared <= r * r) {
                    canDetect = true;
                    break;  // 找到一个能探测到的就够了
                }
            }
            
            if(canDetect) {
                count++;
            }
        }
    }
    
    cout << count << endl;
    
    return 0;
}