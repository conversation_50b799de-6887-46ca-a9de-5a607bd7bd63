#include <iostream>
using namespace std;

int main() {
    int K;
    cin >> K;
    
    bool found = false;  // 标记是否找到满足条件的数
    
    // 遍历10000到30000之间的所有五位数
    for(int num = 10000; num <= 30000; num++) {
        // 提取五位数的每一位数字
        int a1 = num / 10000;           // 万位
        int a2 = (num / 1000) % 10;     // 千位
        int a3 = (num / 100) % 10;      // 百位
        int a4 = (num / 10) % 10;       // 十位
        int a5 = num % 10;              // 个位
        
        // 构造三个子数
        int sub1 = a1 * 100 + a2 * 10 + a3;  // a1a2a3
        int sub2 = a2 * 100 + a3 * 10 + a4;  // a2a3a4
        int sub3 = a3 * 100 + a4 * 10 + a5;  // a3a4a5
        
        // 检查三个子数是否都能被K整除
        if(sub1 % K == 0 && sub2 % K == 0 && sub3 % K == 0) {
            cout << num << endl;
            found = true;
        }
    }
    
    // 如果没有找到满足条件的数，输出No
    if(!found) {
        cout << "No" << endl;
    }
    
    return 0;
}