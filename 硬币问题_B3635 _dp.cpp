#include <iostream>
#include <algorithm>
using namespace std;

int main() {
    int n;
    cin >> n;
    
    // dp[i] 表示凑出金额 i 所需的最少硬币数
    int dp[1000001];
    
    // 硬币面值
    int coins[3] = {1, 5, 11};
    
    // 初始化：除了0元需要0个硬币，其他都设为无穷大
    dp[0] = 0;
    for(int i = 1; i <= n; i++) {
        dp[i] = 1000000; // 用一个很大的数表示无穷大
    }
    
    // 动态规划
    for(int i = 1; i <= n; i++) {
        for(int j = 0; j < 3; j++) {
            if(i >= coins[j]) {
                dp[i] = min(dp[i], dp[i - coins[j]] + 1);
            }
        }
    }
    
    cout << dp[n] << endl;
    return 0;
}