#include <iostream>
#include <algorithm>
using namespace std;

int main() {
    int missiles[100005];
    int n = 0;
    
    // 读取所有导弹高度
    while(cin >> missiles[n]) {
        n++;
    }
    
    // 第一问：用动态规划求最长非递增子序列
    int dp[100005];
    for(int i = 0; i < n; i++) {
        dp[i] = 1; // 每个导弹至少能拦截自己
    }
    
    // 动态规划：dp[i]表示以第i个导弹结尾的最长拦截序列长度
    for(int i = 1; i < n; i++) {
        for(int j = 0; j < i; j++) {
            // 如果前面的导弹高度 >= 当前导弹高度，可以拦截
            if(missiles[j] >= missiles[i]) {
                dp[i] = max(dp[i], dp[j] + 1);
            }
        }
    }
    
    // 找出最长拦截序列的长度
    int maxIntercept = 0;
    for(int i = 0; i < n; i++) {
        maxIntercept = max(maxIntercept, dp[i]);
    }
    
    // 第二问：用贪心算法求最少系统数
    // 思路：维护每套系统的最后拦截高度，新导弹尽量分配给合适的系统
    int systems[100005]; // systems[i]表示第i套系统最后拦截的导弹高度
    int systemCount = 0;  // 当前使用的系统数量

    // 对每个导弹，找一个合适的系统来拦截
    for(int i = 0; i < n; i++) {
        int pos = -1;

        // 找一个系统，其最后拦截高度 >= 当前导弹高度
        // 在满足条件的系统中，选择最后拦截高度最小的（贪心策略）
        for(int j = 0; j < systemCount; j++) {
            if(systems[j] >= missiles[i]) {
                if(pos == -1 || systems[j] < systems[pos]) {
                    pos = j;
                }
            }
        }

        if(pos == -1) {
            // 没有合适的系统，需要新增一套
            systems[systemCount] = missiles[i];
            systemCount++;
        } else {
            // 使用找到的系统，更新其最后拦截高度
            systems[pos] = missiles[i];
        }
    }
    。
    cout << maxIntercept << endl;
    cout << systemCount << endl;
    
    return 0;
}