#include <iostream>
using namespace std;

int main() {
    int N, M;
    cin >> N >> M;
    
    int squares = 0;    // 正方形个数
    int rectangles = 0; // 长方形个数
    
    // 计算正方形个数
    // 边长从1到min(N,M)的所有正方形
    int maxSide = (N < M) ? N : M;  // 最大可能的正方形边长
    for(int k = 1; k <= maxSide; k++) {
        // k×k正方形的个数 = (N-k+1) × (M-k+1)
        squares += (N - k + 1) * (M - k + 1);
    }
    
    // 计算长方形个数（不包括正方形）
    // 遍历所有可能的长和宽
    for(int h = 1; h <= N; h++) {
        for(int w = 1; w <= M; w++) {
            // 只计算长宽不等的矩形
            if(h != w) {
                // h×w长方形的个数 = (N-h+1) × (M-w+1)
                rectangles += (N - h + 1) * (M - w + 1);
            }
        }
    }
    
    cout << squares << " " << rectangles << endl;
    
    return 0;
}