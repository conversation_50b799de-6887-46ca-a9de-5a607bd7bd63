#include <iostream>
using namespace std;

int main() {
    int n, m;
    cin >> n >> m;
    
    int a[3005];  // 存储刺痛值
    
    // 读取所有刺痛值
    for(int i = 0; i < n; i++) {
        cin >> a[i];
    }
    
    // 特殊情况：如果m为0，输出0
    if(m == 0) {
        cout << 0 << endl;
        return 0;
    }
    
    // 计算第一个窗口的和
    int currentSum = 0;
    for(int i = 0; i < m; i++) {
        currentSum += a[i];
    }
    
    int minSum = currentSum;  // 初始化最小值
    
    // 滑动窗口：从第二个窗口开始
    for(int i = 1; i <= n - m; i++) {
        // 移除窗口左边的元素，添加窗口右边的元素
        currentSum = currentSum - a[i - 1] + a[i + m - 1];
        
        // 更新最小值
        if(currentSum < minSum) {
            minSum = currentSum;
        }
    }
    
    cout << minSum << endl;
    
    return 0;
}