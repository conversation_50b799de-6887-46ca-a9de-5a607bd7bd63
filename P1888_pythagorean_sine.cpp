#include <iostream>
#include <algorithm>
using namespace std;

// 求最大公约数（用于约分）
int gcd(int a, int b) {
    while(b != 0) {
        int temp = b;
        b = a % b;
        a = temp;
    }
    return a;
}

int main() {
    int a, b, c;
    cin >> a >> b >> c;
    
    // 将三个数排序，确定哪个是斜边
    int sides[3] = {a, b, c};
    sort(sides, sides + 3);
    
    // sides[2]是最大边（斜边），sides[0]和sides[1]是直角边
    int leg1 = sides[0];    // 较短直角边
    int leg2 = sides[1];    // 较长直角边  
    int hypotenuse = sides[2];  // 斜边
    
    // 较小锐角的正弦值 = 较短直角边 / 斜边
    int numerator = leg1;      // 分子
    int denominator = hypotenuse;  // 分母
    
    // 约分：除以最大公约数
    int g = gcd(numerator, denominator);
    numerator /= g;
    denominator /= g;
    
    cout << numerator << "/" << denominator << endl;
    
    return 0;
}