#include <iostream>
using namespace std;

int main() {
    int n, m;
    cin >> n >> m;
    
    int a[3005];      // 存储刺痛值
    int prefix[3006]; // 前缀和数组，多开一位方便计算
    
    // 读取所有刺痛值
    for(int i = 0; i < n; i++) {
        cin >> a[i];
    }
    
    // 特殊情况处理
    if(m == 0) {
        cout << 0 << endl;
        return 0;
    }
    
    // 计算前缀和
    prefix[0] = 0;  // prefix[0] = 0，表示前0个元素的和
    for(int i = 1; i <= n; i++) {
        prefix[i] = prefix[i-1] + a[i-1];
    }
    
    // 找最小的连续m个数的和
    int minSum = prefix[m] - prefix[0];  // 第一个区间[0, m-1]的和
    
    for(int i = 1; i <= n - m; i++) {
        // 区间[i, i+m-1]的和 = prefix[i+m] - prefix[i]
        int currentSum = prefix[i + m] - prefix[i];
        if(currentSum < minSum) {
            minSum = currentSum;
        }
    }
    
    cout << minSum << endl;
    
    return 0;
}