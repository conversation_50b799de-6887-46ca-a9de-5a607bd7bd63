#include <iostream>
using namespace std;

int main() {
    int n, k;
    cin >> n >> k;
    
    int totalSmoked = n;  // 总共抽的烟数，初始为n根
    int butts = n;        // 当前烟蒂数，抽完n根烟得到n个烟蒂
    
    // 模拟用烟蒂换烟的过程
    while(butts >= k) {
        int newCigs = butts / k;     // 能换到的新烟数
        totalSmoked += newCigs;      // 累加抽烟总数
        butts = butts % k + newCigs; // 剩余烟蒂 + 抽完新烟产生的烟蒂
    }
    
    cout << totalSmoked << endl;
    
    return 0;
}